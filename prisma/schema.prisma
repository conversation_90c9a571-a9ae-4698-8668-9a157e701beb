generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "darwin-arm64", "linux-musl-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model users {
  id                 Int                  @id @default(autoincrement())
  username           String               @unique
  password           String
  name               String
  first_name         String?
  lastname_name      String?
  email              String?
  img_url            String?
  create_at          DateTime?
  update_at          DateTime?
  // update_at          DateTime?            @default(dbgenerated("NOW()"))
  role_id            Int
  role               roles?               @relation(fields: [role_id], references: [id])
  wallets            wallets[]
  transaction_groups transaction_groups[]
  transaction_swaps  transaction_swaps[]
}

model roles {
  id           Int      @id @default(autoincrement())
  name         String
  isSuperAdmin <PERSON>? @default(false)
  isAd<PERSON>? @default(false)
  isUser       Boolean? @default(false)
  isVisitor    Boolean? @default(false)
  create_at    DateTime
  update_at    DateTime
  users        users[]
}

model banks {
  id        Int       @id @default(autoincrement())
  name      String
  code      String
  img_url   String?
  create_at DateTime
  update_at DateTime
  wallets   wallets[]
}

model wallet_types {
  id        Int       @id @default(autoincrement())
  name      String
  create_at DateTime
  update_at DateTime
  wallets   wallets[]
}

model wallets {
  id             Int            @id @default(autoincrement())
  name           String
  fullname       String?
  account_number String?
  exp_date       DateTime?
  balance        Float          @default(0)
  credit         Float          @default(0)
  img_url        String?
  bank_id        Int
  bank           banks          @relation(fields: [bank_id], references: [id])
  wallet_type_id Int
  wallet_type    wallet_types   @relation(fields: [wallet_type_id], references: [id])
  user_id        Int
  user           users          @relation(fields: [user_id], references: [id], onDelete: Cascade)
  create_at      DateTime
  update_at      DateTime
  transactions   transactions[]
}

model transaction_types {
  id                 Int                  @id @default(autoincrement())
  name               String
  isSwap             Boolean?             @default(false)
  create_at          DateTime
  update_at          DateTime
  transaction_groups transaction_groups[]
}

model transaction_groups {
  id                  Int                  @id @default(autoincrement())
  name                String
  transaction_type_id Int
  transaction_type    transaction_types    @relation(fields: [transaction_type_id], references: [id])
  user_id             Int
  user                users                @relation(fields: [user_id], references: [id], onDelete: Cascade)
  create_at           DateTime
  update_at           DateTime
  transaction_topics  transaction_topics[]
}

model transaction_topics {
  id                   Int                @id @default(autoincrement())
  name                 String
  transaction_group_id Int
  transaction_group    transaction_groups @relation(fields: [transaction_group_id], references: [id], onDelete: Cascade)
  create_at            DateTime
  update_at            DateTime
  transactions         transactions[]
  installments         installments[]
  regularly            regularly[]
}

model transactions {
  id                        Int                @id @default(autoincrement())
  name                      String
  description               String?            @default("")
  current_balance           Float              @default(0)
  amounts                   Float              @default(0)
  transaction_date_time     DateTime
  wallet_id                 Int
  wallet                    wallets            @relation(fields: [wallet_id], references: [id], onDelete: Cascade)
  topic_id                  Int
  transaction_topic         transaction_topics @relation(fields: [topic_id], references: [id], onDelete: Cascade)
  status_id                 Int
  transaction_status        transaction_status @relation(fields: [status_id], references: [id])
  installment_id            Int?
  installment               installments?      @relation(fields: [installment_id], references: [id], onDelete: Cascade)
  regularly_id              Int?
  regularly                 regularly?         @relation(fields: [regularly_id], references: [id], onDelete: Cascade)
  installment_period_number Int?
  transaction_swap          transaction_swaps? @relation(fields: [swap_id], references: [id], onDelete: Cascade)
  swap_id                   Int?
  create_at                 DateTime
  update_at                 DateTime
}

model transaction_status {
  id           Int            @id @default(autoincrement())
  name         String
  isSuccess    Boolean        @default(false)
  isPending    Boolean        @default(false)
  isCancel     Boolean        @default(false)
  isRefund     Boolean        @default(false)
  create_at    DateTime
  update_at    DateTime
  transactions transactions[]
}

model transaction_swaps {
  id           Int            @id @default(autoincrement())
  name         String
  description  String?        @default("")
  user_id      Int
  user         users          @relation(fields: [user_id], references: [id], onDelete: Cascade)
  create_at    DateTime
  update_at    DateTime
  transactions transactions[]
}

model installments {
  id                Int                @id @default(autoincrement())
  name              String
  description       String?            @default("")
  amount            Float              @default(0)
  period            Float              @default(0)
  interes           Float              @default(0)
  topic_id          Int
  transaction_topic transaction_topics @relation(fields: [topic_id], references: [id])
  create_at         DateTime
  update_at         DateTime
  transactions      transactions[]
}

model regularly {
  id                Int                @id @default(autoincrement())
  name              String
  description       String?            @default("")
  date_select       DateTime
  current_date      DateTime
  type_id           Int
  type              regularly_type     @relation(fields: [type_id], references: [id])
  unit_id           Int
  unit              regularly_unit     @relation(fields: [unit_id], references: [id])
  topic_id          Int
  transaction_topic transaction_topics @relation(fields: [topic_id], references: [id])
  create_at         DateTime
  update_at         DateTime
  transactions      transactions[]
}

model regularly_type {
  id          Int         @id @default(autoincrement())
  name        String
  description String?     @default("")
  create_at   DateTime
  update_at   DateTime
  regularly   regularly[]
}

model regularly_unit {
  id          Int         @id @default(autoincrement())
  name        String
  description String?     @default("")
  create_at   DateTime
  update_at   DateTime
  regularly   regularly[]
}
