import {
  PrismaClient,
  transaction_groups,
  transaction_swaps,
  transactions,
} from '@prisma/client';
import { current_datetime } from '../Utils/time';
import _ from 'lodash';
import { createTransaction } from './transaction.service';
const prisma = new PrismaClient();

export const getTransactionSwaps = async (params: {
  page: number;
  size: number;
  search?: string;
  userId?: number;
  startDate?: Date;
  endDate?: Date;
}) => {
  const { page, size, search, userId, startDate, endDate } = params;
  const response: transaction_swaps[] = await prisma.transaction_swaps.findMany(
    {
      skip: (page - 1) * size,
      take: size,
      orderBy: {
        create_at: 'desc',
      },
      where: {
        name: {
          contains: search,
        },
        user_id: userId,
        create_at: {
          lte: endDate,
          gte: startDate,
        },
      },
      include: {
        transactions: true,
      },
    },
  );
  const amount = await prisma.transaction_swaps.count({
    where: {
      name: {
        contains: search,
      },
      user_id: userId,
      create_at: {
        lte: endDate,
        gte: startDate,
      },
    },
  });
  return { data: response, amount: amount };
};

export const createTransactionSwap = async (data: transaction_swaps | any) => {
  data.create_at = current_datetime();
  data.update_at = current_datetime();
  const swap_data: any = _.omit(data, ['transactions']);
  const response: any = await prisma.transaction_swaps
    .create({
      data: swap_data,
    })
    .then((res: any) => {
      if (data.transactions.length > 0) {
        data.transactions.map(async (transaction: transactions) => {
          transaction.swap_id = res.id;
          await createTransaction(transaction);
        });
      }
      return res;
    });
  return response;
};

export const updateTransactionGroup = async (id: number, data: any) => {
  data.update_at = current_datetime();
  const response: transaction_groups = await prisma.transaction_groups.update({
    where: {
      id: id,
    },
    data: data,
  });
  return response;
};

export const deleteTransactionSwap = async (id: number) => {
  const response: transaction_swaps = await prisma.transaction_swaps.delete({
    where: {
      id: id,
    },
  });
  return response;
};
