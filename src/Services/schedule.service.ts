import { PrismaClient } from '@prisma/client';
import moment from 'moment-timezone';
import _ from 'lodash';
import { MONTHS } from '../Utils/time';
const prisma = new PrismaClient();

export const getMonthViewTransactions = async (params: {
  userId?: number;
  walletId?: number;
  topicId?: number;
  statusId?: number;
  installmentId?: number;
  regularlyId?: number;
  startDate?: Date;
  endDate?: Date;
  transactionType?: number;
}) => {
  const {
    userId,
    walletId,
    topicId,
    statusId,
    installmentId,
    regularlyId,
    startDate,
    endDate,
    transactionType,
  } = params;

  const dateList = await prisma.transactions.groupBy({
    by: ['transaction_date_time'],
    orderBy: {
      transaction_date_time: 'asc',
    },
    where: {
      wallet: {
        user_id: userId,
      },
      wallet_id: walletId,
      topic_id: topicId,
      status_id: statusId,
      installment_id: installmentId,
      regularly_id: regularlyId,
      transaction_date_time: {
        lte: endDate,
        gte: startDate,
      },
      transaction_topic: {
        transaction_group: {
          transaction_type_id: transactionType,
        },
      },
    },
  });

  const typeList = await prisma.transaction_types.findMany();

  const transactionList = await prisma.transactions.findMany({
    orderBy: {
      transaction_date_time: 'asc',
    },
    select: {
      id: true,
      name: true,
      description: true,
      current_balance: true,
      amounts: true,
      transaction_date_time: true,
      swap_id: true,
      wallet: {
        select: {
          id: true,
          name: true,
          balance: true,
          img_url: true,
          bank: {
            select: {
              id: true,
              name: true,
            },
          },
          wallet_type: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      },
      transaction_topic: {
        select: {
          id: true,
          name: true,
          transaction_group: {
            select: {
              id: true,
              name: true,
              transaction_type: {
                select: {
                  id: true,
                  name: true,
                  isSwap: true,
                },
              },
            },
          },
        },
      },
    },
    where: {
      wallet: {
        user_id: userId,
      },
      wallet_id: walletId,
      topic_id: topicId,
      status_id: statusId,
      installment_id: installmentId,
      regularly_id: regularlyId,
      transaction_date_time: {
        lte: endDate,
        gte: startDate,
      },
      transaction_topic: {
        transaction_group: {
          transaction_type_id: transactionType,
        },
      },
    },
  });

  const result: any = [];

  dateList.map((res: any) => {
    const date_format = moment(res.transaction_date_time).format('YYYY-MM-DD');
    const byTypeList: any = [];
    typeList.map((type: any) => {
      const filter = _.filter(transactionList, (o: any) => {
        return (
          date_format ===
            moment(o.transaction_date_time).format('YYYY-MM-DD') &&
          type.id === o.transaction_topic.transaction_group.transaction_type.id
        );
      });
      const sum = Number(
        _.sumBy(filter, (a: any) => {
          return a.amounts;
        }),
      );
      byTypeList.push({
        transaction_type: type.name,
        transaction_number: filter.length,
        total: type.id === 2 ? sum * -1 : sum,
      });
    });

    const dateResult = _.filter(transactionList, (o: any) => {
      return (
        date_format === moment(o.transaction_date_time).format('YYYY-MM-DD')
      );
    });

    const gResult = _.chain(dateResult)
      .groupBy('transaction_topic.transaction_group.id')
      .map((value, key) => {
        const group = value[0].transaction_topic.transaction_group;
        const type =
          value[0].transaction_topic.transaction_group.transaction_type;

        const sum = Number(
          _.sumBy(value, (a: any) => {
            if (type.isSwap) {
              if (a.amounts > 0) {
                return a.amounts;
              }
            } else {
              return a.amounts;
            }
          }),
        );

        return {
          transaction_group_id: Number(key),
          transaction_group: group.name,
          transaction_type_id: type.id,
          isSwap: type.isSwap,
          transaction_type: type.name,
          transaction_number: value.length,
          total: type.id === 2 ? sum * -1 : sum,
        };
      })
      .value();

    result.push({
      date: date_format,
      count: Number(
        _.sumBy(gResult, (a: any) => {
          return a.transaction_number;
        }),
      ),
      totalOfDay: Number(
        _.sumBy(gResult, (a: any) => {
          if (!a.isSwap) {
            return a.total;
          }
        }),
      ),
      summary: gResult,
    });
  });

  return result;
};

export const getYearViewTransactions = async (params: {
  userId?: number;
  walletId?: number;
  topicId?: number;
  statusId?: number;
  installmentId?: number;
  regularlyId?: number;
  startDate?: Date;
  endDate?: Date;
  transactionType?: number;
}) => {
  const {
    userId,
    walletId,
    topicId,
    statusId,
    installmentId,
    regularlyId,
    startDate,
    endDate,
    transactionType,
  } = params;

  const dateList = await prisma.transactions.groupBy({
    by: ['transaction_date_time'],
    orderBy: {
      transaction_date_time: 'asc',
    },
    where: {
      wallet: {
        user_id: userId,
      },
      wallet_id: walletId,
      topic_id: topicId,
      status_id: statusId,
      installment_id: installmentId,
      regularly_id: regularlyId,
      transaction_date_time: {
        lte: endDate,
        gte: startDate,
      },
      transaction_topic: {
        transaction_group: {
          transaction_type_id: transactionType,
        },
      },
    },
  });

  const months: any = _.uniqBy(
    dateList.map((date: any) => {
      return {
        month_no: moment(date.transaction_date_time).format('MM'),
        month_en: moment(date.transaction_date_time).format('MMMM'),
        month_th: _.find(MONTHS, (m: any) => {
          return m.no === moment(date.transaction_date_time).format('MM');
        }).th,
      };
    }),
    (o: any) => {
      return o.month_no;
    },
  );

  // const typeList = await prisma.transaction_types.findMany();

  const transactionList = await prisma.transactions.findMany({
    orderBy: {
      transaction_date_time: 'asc',
    },
    select: {
      id: true,
      name: true,
      description: true,
      current_balance: true,
      amounts: true,
      transaction_date_time: true,
      wallet: {
        select: {
          id: true,
          name: true,
          balance: true,
          img_url: true,
          bank: {
            select: {
              id: true,
              name: true,
            },
          },
          wallet_type: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      },
      transaction_topic: {
        select: {
          id: true,
          name: true,
          transaction_group: {
            select: {
              id: true,
              name: true,
              transaction_type: {
                select: {
                  id: true,
                  name: true,
                  isSwap: true,
                },
              },
            },
          },
        },
      },
    },
    where: {
      wallet: {
        user_id: userId,
      },
      wallet_id: walletId,
      topic_id: topicId,
      status_id: statusId,
      installment_id: installmentId,
      regularly_id: regularlyId,
      transaction_date_time: {
        lte: endDate,
        gte: startDate,
      },
      transaction_topic: {
        transaction_group: {
          transaction_type_id: transactionType,
        },
      },
    },
  });

  const result: any = [];

  months.map((res: any) => {
    // const byTypeList: any = [];
    // typeList.map((type: any) => {
    //   const filter = _.filter(transactionList, (o: any) => {
    //     return (
    //       res.month_no === moment(o.transaction_date_time).format('MM') &&
    //       type.id === o.transaction_topic.transaction_group.transaction_type.id
    //     );
    //   });
    //   const sum = Number(
    //     _.sumBy(filter, (a: any) => {
    //       return a.amounts;
    //     }),
    //   );
    //   byTypeList.push({
    //     transaction_type: type.name,
    //     transaction_number: filter.length,
    //     total: type.id === 2 ? sum * -1 : sum,
    //   });
    // });

    const monthResult = _.filter(transactionList, (o: any) => {
      return res.month_no === moment(o.transaction_date_time).format('MM');
    });

    const gResult = _.chain(monthResult)
      .groupBy('transaction_topic.transaction_group.id')
      .map((value, key) => {
        const group = value[0].transaction_topic.transaction_group;
        const type =
          value[0].transaction_topic.transaction_group.transaction_type;

        const sum = Number(
          _.sumBy(value, (a: any) => {
            if (type.isSwap) {
              if (a.amounts > 0) {
                return a.amounts;
              }
            } else {
              return a.amounts;
            }
          }),
        );

        return {
          transaction_group_id: Number(key),
          transaction_group: group.name,
          transaction_type_id: type.id,
          isSwap: type.isSwap,
          transaction_type: type.name,
          transaction_number: value.length,
          total: type.id === 2 ? sum * -1 : sum,
        };
      })
      .value();

    result.push({
      content: {
        en: {
          index: 'Month',
          value: res.month_en,
        },
        th: {
          index: 'เดือน',
          value: res.month_th,
        },
      },
      count: Number(
        _.sumBy(gResult, (a: any) => {
          return a.transaction_number;
        }),
      ),
      total: Number(
        _.sumBy(gResult, (a: any) => {
          if (!a.isSwap) {
            return a.total;
          }
        }),
      ),
      summary: gResult,
    });
  });
  return result;
};

export const getSummary = async (params: {
  userId?: number;
  walletId?: number;
  topicId?: number;
  statusId?: number;
  installmentId?: number;
  regularlyId?: number;
  startDate?: Date;
  endDate?: Date;
  transactionType?: number;
}) => {
  const {
    userId,
    walletId,
    topicId,
    statusId,
    installmentId,
    regularlyId,
    startDate,
    endDate,
    transactionType,
  } = params;

  const typeList = await prisma.transaction_types.findMany({
    orderBy: {
      id: 'asc',
    },
    select: {
      id: true,
      name: true,
      isSwap: true,
      transaction_groups: {
        select: {
          id: true,
          name: true,
          transaction_type_id: true,
          user_id: true,
          transaction_topics: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      },
    },
  });

  const transactionList = await prisma.transactions.findMany({
    orderBy: {
      transaction_date_time: 'asc',
    },
    select: {
      id: true,
      name: true,
      description: true,
      current_balance: true,
      amounts: true,
      transaction_date_time: true,
      wallet: {
        select: {
          id: true,
          name: true,
          balance: true,
          img_url: true,
          bank: {
            select: {
              id: true,
              name: true,
            },
          },
          wallet_type: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      },
      transaction_topic: {
        select: {
          id: true,
          name: true,
          transaction_group: {
            select: {
              id: true,
              name: true,
              transaction_type: {
                select: {
                  id: true,
                  name: true,
                  isSwap: true,
                },
              },
            },
          },
        },
      },
    },
    where: {
      wallet: {
        user_id: userId,
      },
      wallet_id: walletId,
      topic_id: topicId,
      status_id: statusId,
      installment_id: installmentId,
      regularly_id: regularlyId,
      transaction_date_time: {
        lte: endDate,
        gte: startDate,
      },
      transaction_topic: {
        transaction_group: {
          transaction_type_id: transactionType,
        },
      },
    },
  });

  const summaryResult: any = [];

  typeList.map((type: any) => {
    const filterType = _.filter(transactionList, (o: any) => {
      return (
        type.id === o.transaction_topic.transaction_group.transaction_type.id
      );
    });
    const sum = Number(
      _.sumBy(filterType, (a: any) => {
        if (type.isSwap) {
          if (a.amounts > 0) {
            return a.amounts;
          }
        } else {
          return a.amounts;
        }
      }),
    );
    const my_groups: any = _.filter(type.transaction_groups, (og: any) => {
      return og.user_id === userId;
    });
    summaryResult.push({
      type_id: type.id,
      transaction_type: type.name,
      transaction_number: filterType.length,
      total: type.id === 2 ? sum * -1 : sum,
      groups: my_groups.map((g: any) => {
        const filterGroup = _.filter(transactionList, (o: any) => {
          return g.id === o.transaction_topic.transaction_group.id;
        });
        const sum_group = Number(
          _.sumBy(filterGroup, (a: any) => {
            if (type.isSwap) {
              if (a.amounts > 0) {
                return a.amounts;
              }
            } else {
              return a.amounts;
            }
          }),
        );
        return {
          group_id: g.id,
          transaction_group: g.name,
          transaction_number: filterGroup.length,
          total: type.id === 2 ? sum_group * -1 : sum_group,
          topic: g.transaction_topics.map((t: any) => {
            const filterTopic: any = _.filter(transactionList, (o: any) => {
              return t.id === o.transaction_topic.id;
            });
            const sum_topic = Number(
              _.sumBy(filterTopic, (a: any) => {
                if (type.isSwap) {
                  if (a.amounts > 0) {
                    return a.amounts;
                  }
                } else {
                  return a.amounts;
                }
              }),
            );
            return {
              topic_id: t.id,
              transaction_topic: t.name,
              transaction_number: filterTopic.length,
              total: type.id === 2 ? sum_topic * -1 : sum_topic,
            };
          }),
        };
      }),
    });
  });

  return summaryResult;
};

export const getTotalViewTransactions = async (params: {
  userId?: number;
  walletId?: number;
  topicId?: number;
  statusId?: number;
  installmentId?: number;
  regularlyId?: number;
  transactionType?: number;
}) => {
  const {
    userId,
    walletId,
    topicId,
    statusId,
    installmentId,
    regularlyId,
    transactionType,
  } = params;

  const dateList = await prisma.transactions.groupBy({
    by: ['transaction_date_time'],
    orderBy: {
      transaction_date_time: 'asc',
    },
    where: {
      wallet: {
        user_id: userId,
      },
      wallet_id: walletId,
      topic_id: topicId,
      status_id: statusId,
      installment_id: installmentId,
      regularly_id: regularlyId,
      transaction_topic: {
        transaction_group: {
          transaction_type_id: transactionType,
        },
      },
    },
  });

  const years: any = _.uniqBy(
    dateList.map((date: any) => {
      return {
        year_en: moment(date.transaction_date_time).format('YYYY'),
        year_th: String(
          Number(moment(date.transaction_date_time).format('YYYY')) + 543,
        ),
      };
    }),
    (o: any) => {
      return o.year_en;
    },
  );

  // const typeList = await prisma.transaction_types.findMany();

  const transactionList = await prisma.transactions.findMany({
    orderBy: {
      transaction_date_time: 'asc',
    },
    select: {
      id: true,
      name: true,
      description: true,
      current_balance: true,
      amounts: true,
      transaction_date_time: true,
      wallet: {
        select: {
          id: true,
          name: true,
          balance: true,
          img_url: true,
          bank: {
            select: {
              id: true,
              name: true,
            },
          },
          wallet_type: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      },
      transaction_topic: {
        select: {
          id: true,
          name: true,
          transaction_group: {
            select: {
              id: true,
              name: true,
              transaction_type: {
                select: {
                  id: true,
                  name: true,
                  isSwap: true,
                },
              },
            },
          },
        },
      },
    },
    where: {
      wallet: {
        user_id: userId,
      },
      wallet_id: walletId,
      topic_id: topicId,
      status_id: statusId,
      installment_id: installmentId,
      regularly_id: regularlyId,
      transaction_topic: {
        transaction_group: {
          transaction_type_id: transactionType,
        },
      },
    },
  });

  const result: any = [];

  years.map((res: any) => {
    // const byTypeList: any = [];
    // typeList.map((type: any) => {
    //   const filter = _.filter(transactionList, (o: any) => {
    //     return (
    //       res.year_en === moment(o.transaction_date_time).format('YYYY') &&
    //       type.id === o.transaction_topic.transaction_group.transaction_type.id
    //     );
    //   });
    //   const sum = Number(
    //     _.sumBy(filter, (a: any) => {
    //       return a.amounts;
    //     }),
    //   );
    //   byTypeList.push({
    //     transaction_type: type.name,
    //     transaction_number: filter.length,
    //     total: type.id === 2 ? sum * -1 : sum,
    //   });
    // });

    const yearResult = _.filter(transactionList, (o: any) => {
      return res.year_en === moment(o.transaction_date_time).format('YYYY');
    });

    const gResult = _.chain(yearResult)
      .groupBy('transaction_topic.transaction_group.id')
      .map((value, key) => {
        const group = value[0].transaction_topic.transaction_group;
        const type =
          value[0].transaction_topic.transaction_group.transaction_type;

        const sum = Number(
          _.sumBy(value, (a: any) => {
            if (type.isSwap) {
              if (a.amounts > 0) {
                return a.amounts;
              }
            } else {
              return a.amounts;
            }
          }),
        );

        return {
          transaction_group_id: Number(key),
          transaction_group: group.name,
          transaction_type_id: type.id,
          isSwap: type.isSwap,
          transaction_type: type.name,
          transaction_number: value.length,
          total: type.id === 2 ? sum * -1 : sum,
        };
      })
      .value();

    result.push({
      content: {
        en: {
          index: 'Year',
          value: res.year_en,
        },
        th: {
          index: 'ปี',
          value: res.year_th,
        },
      },
      count: Number(
        _.sumBy(gResult, (a: any) => {
          return a.transaction_number;
        }),
      ),
      total: Number(
        _.sumBy(gResult, (a: any) => {
          if (!a.isSwap) {
            return a.total;
          }
        }),
      ),
      summary: gResult,
    });
  });
  return result;
};
