{"name": "moneylab-api", "version": "1.0.0", "description": "", "main": "src/index.ts", "scripts": {"build": "env-cmd -f .env tsc && tsc-alias", "build:docker": "tsc && tsc-alias", "start": "env-cmd -f .env node dist/index.js", "start:docker": "node dist/index.js", "dev": "env-cmd -f .env.dev nodemon src/index.ts", "lint": "eslint src/**/*.ts", "format": "eslint src/**/*.ts --fix", "prisma": "npx prisma migrate dev --name init"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@prisma/client": "^5.9.1", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/lodash": "^4.14.202", "@types/morgan": "^1.9.9", "@types/node": "^20.10.7", "@typescript-eslint/eslint-plugin": "^6.18.1", "@typescript-eslint/parser": "^6.18.1", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "cors": "^2.8.5", "env-cmd": "^10.1.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-node": "^11.0.0", "eslint-plugin-prettier": "^5.1.2", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-standard": "^5.0.0", "eslint-plugin-unused-imports": "^3.0.0", "express": "^4.18.2", "fs": "^0.0.1-security", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "moment-timezone": "^0.5.44", "morgan": "^1.10.0", "nodemon": "^3.0.2", "prettier": "^3.1.1", "prisma": "^6.13.0", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "tsc-alias": "^1.8.8", "typescript": "^5.3.3", "xlsx": "^0.18.5"}, "dependencies": {"prettier-eslint": "^16.2.0", "save-dev": "^0.0.1-security"}}