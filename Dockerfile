FROM node:18-alpine AS deps
WORKDIR /app
COPY package.json ./
COPY package-lock.json ./
RUN apk add --no-cache openssl
RUN npm ci
COPY prisma ./prisma/
RUN npx prisma generate

# Rebuild the source code only when needed
FROM node:18-alpine AS builder
WORKDIR /app
COPY . .
COPY --from=deps /app/node_modules ./node_modules
# Generate Prisma client in builder stage to ensure types are available for TypeScript compilation
RUN npx prisma generate
RUN npm run build:docker

# Production image, copy all the files and run the app
FROM node:18-alpine AS runner
WORKDIR /app
RUN apk add --no-cache openssl

COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json package.json
COPY --from=builder /app/prisma ./prisma

EXPOSE 3000
CMD ["npm", "run", "start:docker"]
